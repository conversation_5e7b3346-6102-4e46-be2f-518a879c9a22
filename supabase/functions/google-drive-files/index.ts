// Supabase Edge Function for Google Drive file operations
// Deploy this to your Supabase project using: supabase functions deploy google-drive-files

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, DELETE',
}

interface GoogleDriveFileRequest {
  action: 'upload' | 'list' | 'delete' | 'get_storage_info'
  file_data?: string // base64 encoded file
  file_name?: string
  file_id?: string
  folder_name?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Google Drive Files function called with action:', req.method)

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Get user from JWT token
    const {
      data: { user },
    } = await supabaseClient.auth.getUser()

    if (!user) {
      console.log('No user found in JWT token')
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    console.log('User authenticated:', user.id)

    // Get fresh access token by refreshing directly
    const storedRefreshToken = user.user_metadata?.google_drive_refresh_token

    if (!storedRefreshToken) {
      return new Response(
        JSON.stringify({ error: 'No refresh token found. Please re-authenticate.' }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Get Google OAuth credentials from environment
    const GOOGLE_CLIENT_ID = Deno.env.get('GOOGLE_CLIENT_ID')
    const GOOGLE_CLIENT_SECRET = Deno.env.get('GOOGLE_CLIENT_SECRET')

    if (!GOOGLE_CLIENT_ID || !GOOGLE_CLIENT_SECRET) {
      return new Response(
        JSON.stringify({ error: 'Google OAuth credentials not configured' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Refresh the access token
    const refreshResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: GOOGLE_CLIENT_ID,
        client_secret: GOOGLE_CLIENT_SECRET,
        refresh_token: storedRefreshToken,
        grant_type: 'refresh_token',
      }),
    })

    if (!refreshResponse.ok) {
      return new Response(
        JSON.stringify({ error: 'Failed to refresh token. Please re-authenticate.' }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    const refreshedTokens = await refreshResponse.json()
    const access_token = refreshedTokens.access_token

    const { action, file_data, file_name, file_id, folder_name }: GoogleDriveFileRequest = await req.json()

    const DRIVE_API_BASE = 'https://www.googleapis.com/drive/v3'
    const UPLOAD_API_BASE = 'https://www.googleapis.com/upload/drive/v3'

    switch (action) {
      case 'upload':
        if (!file_data || !file_name) {
          return new Response(
            JSON.stringify({ error: 'File data and name required' }),
            {
              status: 400,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            }
          )
        }

        // Convert base64 to blob
        const fileBlob = new Blob([Uint8Array.from(atob(file_data), c => c.charCodeAt(0))])

        // Create or find nested folder structure
        let folderId = ''
        if (folder_name) {
          const folderParts = folder_name.split('/')
          let currentParentId = ''

          for (const folderPart of folderParts) {
            if (!folderPart.trim()) continue

            // Search for existing folder
            const searchQuery = currentParentId
              ? `name='${folderPart}' and mimeType='application/vnd.google-apps.folder' and '${currentParentId}' in parents`
              : `name='${folderPart}' and mimeType='application/vnd.google-apps.folder' and parents in 'root'`

            const folderResponse = await fetch(`${DRIVE_API_BASE}/files?q=${encodeURIComponent(searchQuery)}`, {
              headers: { 'Authorization': `Bearer ${access_token}` }
            })

            const folderData = await folderResponse.json()

            if (folderData.files && folderData.files.length > 0) {
              currentParentId = folderData.files[0].id
            } else {
              // Create folder
              const createFolderResponse = await fetch(`${DRIVE_API_BASE}/files`, {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${access_token}`,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  name: folderPart,
                  mimeType: 'application/vnd.google-apps.folder',
                  parents: currentParentId ? [currentParentId] : ['root'],
                }),
              })

              if (!createFolderResponse.ok) {
                const error = await createFolderResponse.text()
                console.error('Failed to create folder:', error)
                throw new Error(`Failed to create folder: ${folderPart}`)
              }

              const newFolder = await createFolderResponse.json()
              currentParentId = newFolder.id
            }
          }

          folderId = currentParentId
        }

        // Upload file
        const metadata = {
          name: file_name,
          parents: folderId ? [folderId] : undefined,
        }

        const form = new FormData()
        form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }))
        form.append('file', fileBlob)

        const uploadResponse = await fetch(`${UPLOAD_API_BASE}/files?uploadType=multipart&fields=id,name,webViewLink,size,createdTime`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${access_token}`,
          },
          body: form,
        })

        if (!uploadResponse.ok) {
          const error = await uploadResponse.text()
          console.error('Upload failed:', error)
          return new Response(
            JSON.stringify({ error: 'Upload failed' }),
            {
              status: 500,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            }
          )
        }

        const uploadedFile = await uploadResponse.json()

        // Make file publicly viewable
        await fetch(`${DRIVE_API_BASE}/files/${uploadedFile.id}/permissions`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${access_token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            role: 'reader',
            type: 'anyone',
          }),
        })

        return new Response(
          JSON.stringify({
            success: true,
            file: uploadedFile,
            embed_url: `https://drive.google.com/file/d/${uploadedFile.id}/preview`,
            direct_url: `https://drive.google.com/uc?export=download&id=${uploadedFile.id}`,
            download_url: `https://drive.google.com/uc?id=${uploadedFile.id}`,
            shareable_url: `https://drive.google.com/file/d/${uploadedFile.id}/view?usp=sharing`,
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )

      case 'list':
        const query = "mimeType contains 'video/'"
        const listResponse = await fetch(`${DRIVE_API_BASE}/files?q=${encodeURIComponent(query)}&fields=files(id,name,webViewLink,size,createdTime,thumbnailLink)&orderBy=createdTime desc`, {
          headers: { 'Authorization': `Bearer ${access_token}` }
        })

        const listData = await listResponse.json()

        return new Response(
          JSON.stringify({
            files: listData.files || [],
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )

      case 'delete':
        if (!file_id) {
          return new Response(
            JSON.stringify({ error: 'File ID required' }),
            {
              status: 400,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            }
          )
        }

        const deleteResponse = await fetch(`${DRIVE_API_BASE}/files/${file_id}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${access_token}` }
        })

        return new Response(
          JSON.stringify({ success: deleteResponse.ok }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )

      case 'get_storage_info':
        const aboutResponse = await fetch(`${DRIVE_API_BASE}/about?fields=storageQuota,user`, {
          headers: { 'Authorization': `Bearer ${access_token}` }
        })

        const aboutData = await aboutResponse.json()

        return new Response(
          JSON.stringify({
            storage_quota: aboutData.storageQuota,
            user: aboutData.user,
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )

      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )
    }

  } catch (error) {
    console.error('Edge function error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})

/* 
To deploy this Edge Function:

1. Deploy the function:
   supabase functions deploy google-drive-files

2. This function depends on the google-drive-auth function for token management

3. The function will be available at:
   https://your-project.supabase.co/functions/v1/google-drive-files
*/
